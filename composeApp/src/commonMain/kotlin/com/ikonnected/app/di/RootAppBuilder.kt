package com.ikonnected.app.di

import com.ikonnected.app.AppContext
import com.ikonnected.app.buildErrorLogger
import com.ikonnected.app.buildFileConverter
import com.ikonnected.app.buildSharedStorage
import com.ikonnected.app.ui.MainViewModel
import com.ikonnected.app.ui.di.MainUiComponent
import com.ikonnected.core.auth.OauthTokenProvider
import com.ikonnected.core.biometrics.buildIkonnectedBiometrics
import com.ikonnected.core.presentation.navigation.compose.NavigatorHost
import com.ikonnected.database.data.di.CoreDatabaseComponent
import com.ikonnected.database.data.di.create
import com.ikonnected.domain.auth.data.di.AuthDomainComponent
import com.ikonnected.domain.auth.data.di.create
import com.ikonnected.feature.auth.ui.di.AuthModule
import com.ikonnected.feature.auth.ui.di.create


internal object RootAppBuilder {

    fun build(
        appContext: AppContext,
        activityContext: Any,
        serverUrl: String
    ): Pair<NavigatorHost.Root, MainViewModel> {

        val authTokenStorage = appContext.buildSharedStorage("elephant")
        val biometricsManager =
            buildIkonnectedBiometrics(activityContext, appContext.buildSharedStorage("biometrics"))


        val tokenProvider = OauthTokenProvider.create(authTokenStorage)
        val networkClient =
            NetworkComponent::client


        val errorLogger = appContext.buildErrorLogger()

        val fileConverter = appContext.buildFileConverter()
        val authModule = AuthModule::class.create()

        val databaseComponent = CoreDatabaseComponent::class.create()

        val authDomainComponent = AuthDomainComponent::class.create(
            client = networkClient.client.httpClient,
            errorLogger = errorLogger,
            tokenProvider = tokenProvider,
            biometricsManager = biometricsManager,
            dbInitializer = DatabaseInitializationListenerImpl(
                appContext = appContext,
                dbNameProvider = {
                    requireNotNull(tokenProvider.getEmail()) { "Email does not exists" }
                }
            )
        )

        val mainViewModel = MainUiComponent::class.create()

        val appComponent =
            ComposeAppComponent::class.create(authModule = authModule)

        return appComponent.root to mainViewModel.viewModel

    }

}